# Agent Guidelines for Gym Scheduler

## Build/Test Commands
- **Build:** `pnpm build` (turbo build across all packages)
- **Dev:** `pnpm dev` (starts Next.js dev server with turbopack)
- **Lint:** `pnpm lint` (ESLint across all packages)
- **Format:** `pnpm format` (Prettier formatting)
- **Type check:** `turbo check-types` (TypeScript type checking)
- **Single app dev:** `pnpm --filter gym-scheduler dev`

## Architecture
- **Monorepo:** Turborepo with pnpm workspaces
- **Main app:** `apps/app` - Next.js 15 gym scheduler with React 19
- **Packages:** `packages/ui` (shared UI components), `eslint-config`, `typescript-config`
- **Tech stack:** Next.js, React, TypeScript, Tailwind CSS, shadcn/ui, Radix UI
- **UI framework:** shadcn/ui components with custom styling

## Code Style
- **Components:** PascalCase names, kebab-case files, `"use client"` for client components
- **Imports:** React hooks first, external libs, then local (UI components, utils)
- **Export:** Named exports: `export function ComponentName`
- **Icons:** Use `@remixicon/react` for consistency
- **Styling:** Tailwind CSS with `cn()` utility for conditional classes
- **Types:** Strict TypeScript, interfaces for props, `type` imports
- **File naming:** kebab-case for components (.tsx), PascalCase for component names
