{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "registry:build": "shadcn build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@remixicon/react": "^4.6.0", "@workspace/auth": "workspace:*", "@workspace/ui": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.6.4", "react-dom": "^19.1.0", "shadcn": "^2.4.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.1", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "postcss": "^8.5.3", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "typescript": "^5.8.2"}}