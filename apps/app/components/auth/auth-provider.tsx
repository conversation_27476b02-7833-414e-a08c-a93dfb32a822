"use client"

import { create<PERSON><PERSON>x<PERSON>, use<PERSON>ontex<PERSON>, ReactNode } from "react"
import { AuthUIProvider, authClient } from "@workspace/auth"
import { useRouter } from "next/navigation"
import Link from "next/link"

const AuthContext = createContext(authClient)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter()

  return (
    <AuthUIProvider
      authClient={authClient}
      navigate={router.push}
      replace={router.replace}
      onSessionChange={() => {
        // Clear router cache (protected routes)
        router.refresh()
      }}
      Link={Link}
    >
      <AuthContext.Provider value={authClient}>{children}</AuthContext.Provider>
    </AuthUIProvider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
